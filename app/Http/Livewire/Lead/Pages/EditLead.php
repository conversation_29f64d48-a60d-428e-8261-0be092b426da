<?php

namespace App\Http\Livewire\Lead\Pages;

use App\Actions\Procedure\GetProcedureDataForCrm;
use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Integrations\Receita\Services\ReceitaService;
use App\Models\City;
use App\Models\Cnae;
use App\Models\CompanyBranch;
use App\Models\Lead;
use App\Models\LeadBranchItem;
use App\Models\LeadCompany;
use App\Models\LeadProposal;
use App\Models\LeadProposalContract;
use App\Models\Procedure;
use App\Models\State;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Livewire\Component;

class EditLead extends Component
{
    public string $action;
    public array $companies = [];
    public array $procedures = [];
    public array $leadCompanyBranchesArray = [];
    public array $leadProposalContractSignatories = [];
    public ?int $leadCompanyId;
    public int $totalEmployeeCount;
    public string $nr04RiskDegree;
    public string $resourceRoute;
    public string $tab;
    public ?int $currentReplicateCompanyProcedureCompanyKey = null;
    public ?int $currentReplicateCompanyProcedureProcedureKey = null;
    public bool $addCompanyModalOpened = false;
    public ?string $leadDealRegisteredAt = null;
    public ?string $leadLossRegisteredAt = null;
    public ?int $latestLeadProposalId = null;
    public ?string $signedLeadProposalContractLink = null;
    public bool $bid = false;
    public array $leadCompanyContacts = [];
    public ?string $leadCompanyContactId = null;

    public Lead $lead;
    public ?LeadProposal $dealLeadProposal = null;
    public ?LeadProposalContract $lastGeneratedLeadProposalContract = null;
    public Collection $crmFunnels;

    public function mount(string $resourceRoute, string $action, Lead $lead, Collection $crmFunnels, ?string $signedLeadProposalContractLink = null): void
    {
        $this->resourceRoute = $resourceRoute;
        $this->action = $action;
        $this->lead = $lead;
        $this->bid = $lead->bid;
        $this->leadCompanyId = $lead->lead_company_id;

        $this->nr04RiskDegree = $this->lead->leadCompany->cnae?->nr04_risk_degree
            ?? $this->lead->leadCompany->company->cnaeModel?->nr04_risk_degree
            ?? '';

        $this->crmFunnels = $crmFunnels;
        $this->latestLeadProposalId = $this->lead->leadProposals()->orderByDesc('id')->first()?->id;
        $this->signedLeadProposalContractLink = $signedLeadProposalContractLink;

        $this->dealLeadProposal = $lead->leadProposals()
            ->where('deal', true)
            ->orderByDesc('id')
            ->first();

        $this->lastGeneratedLeadProposalContract = LeadProposalContract::query()
            ->whereRelation('leadProposal', 'lead_id', $lead->id)
            ->orderByDesc('id')
            ->first();

        /** @var \App\Models\LeadCompany $leadCompany */
        $leadCompany = LeadCompany::find($this->leadCompanyId);

        $leadCompany->load('leadCompanyContacts');

        $this->leadCompanyContacts = $leadCompany->leadCompanyContacts->toArray();
        $this->leadCompanyContactId = $lead->lead_company_contact_id;

        /** @var \App\Models\Company $company */
        $company = $leadCompany->company;

        if ($company) {
            $this->leadCompanyBranchesArray = array_merge(
                array_search($company->branch_name, array_column($this->companies, 'branch_name')) !== false
                    ? [$company->id => $company->branch_name]
                    : [],
                $company->companyBranches
                    ->filter(function (CompanyBranch $companyBranch) use ($company): bool {
                        return !is_null($companyBranch->branch)
                            && $companyBranch->branch->technical_system === $company->technical_system
                            && !in_array($companyBranch->branch->branch_name, array_column($this->companies, 'branch_name'));
                    })
                    ->mapWithKeys(fn(CompanyBranch $companyBranch): array => [$companyBranch->branch->branch_name => $companyBranch->branch->branch_name])
                    ->toArray()
            );
        } else {
            // If lead company has no Company relationship, load lead company branches
            $leadCompany->load('leadCompanyBranches');

            $this->leadCompanyBranchesArray = $leadCompany->leadCompanyBranches
                ->filter(function ($leadCompanyBranch): bool {
                    return !in_array($leadCompanyBranch->branch_name, array_column($this->companies, 'branch_name'));
                })
                ->mapWithKeys(fn($leadCompanyBranch): array => [$leadCompanyBranch->branch_name => $leadCompanyBranch->branch_name])
                ->toArray();
        }

        foreach ($this->lead->leadItems as $leadItem) {
            $this->procedures[] = [
                'lead_proposal_item_id' => $leadItem->id,
                'lead_proposal_item_procedure_id' => $leadItem->procedure_id,
                'lead_proposal_item_procedure_name' => $leadItem->procedure->name,
                'lead_proposal_item_type' => $leadItem->type,
                'lead_proposal_item_quantity' => $leadItem->quantity,
                'lead_proposal_item_minimum_amount' => $leadItem->friendly_minimum_amount,
                'lead_proposal_item_unit_amount' => $leadItem->unit_amount,
                'lead_proposal_item_discount_percentage' => $leadItem->discount_percentage,
                'lead_proposal_item_discount_amount' => $leadItem->discount_amount,
                'lead_proposal_item_addition_percentage' => $leadItem->addition_percentage,
                'lead_proposal_item_addition_amount' => $leadItem->addition_amount,
                'lead_proposal_item_total_amount' => $leadItem->total_amount,
            ];
        }

        foreach ($this->lead->leadBranches as $leadBranch) {
            $this->companies[] = [
                'id' => $leadBranch->id,
                'company_name' => $leadBranch->name,
                'company_trading_name' => $leadBranch->trading_name,
                'company_tax_id_number' => $leadBranch->tax_id_number,
                'branch_name' => $leadBranch->branch_name,
                'employee_count' => $leadBranch->employee_count,
                'address_zipcode' => $leadBranch->zipcode,
                'address_address' => $leadBranch->address,
                'address_number' => $leadBranch->number,
                'address_additional_info' => $leadBranch->additional_info,
                'address_district' => $leadBranch->district,
                'address_city_id' => $leadBranch->city_id,
                'address_city_name' => $leadBranch->city,
                'address_state_id' => $leadBranch->state_id,
                'address_state_abbreviation' => $leadBranch->state,
                'cnae_id' => $leadBranch->cnae_id,
                'head_office' => $leadBranch->lead->leadCompany->company
                    ? $leadBranch->lead->leadCompany->company->companyBranches
                    ->filter(function (CompanyBranch $companyBranch) use ($company, $leadBranch): bool {
                        return !is_null($companyBranch->branch)
                            && $companyBranch->branch->technical_system === $company->technical_system
                            && $companyBranch->branch->tax_id_number === $leadBranch->tax_id_number;
                    })->count() > 0
                    : false,
                'procedures' => $leadBranch->leadBranchItems
                    ->map(fn(LeadBranchItem $leadBranchItem): array => [
                        'lead_proposal_company_item_id' => $leadBranchItem->id,
                        'lead_proposal_company_item_procedure_id' => $leadBranchItem->procedure_id,
                        'lead_proposal_company_item_procedure_name' => $leadBranchItem->procedure->name,
                        'lead_proposal_company_item_type' => $leadBranchItem->type,
                        'lead_proposal_company_item_quantity' => $leadBranchItem->quantity,
                        'lead_proposal_company_item_unit_amount' => $leadBranchItem->unit_amount,
                        'lead_proposal_company_item_discount_percentage' => $leadBranchItem->discount_percentage,
                        'lead_proposal_company_item_discount_amount' => $leadBranchItem->discount_amount,
                        'lead_proposal_company_item_addition_percentage' => $leadBranchItem->addition_percentage,
                        'lead_proposal_company_item_addition_amount' => $leadBranchItem->addition_amount,
                        'lead_proposal_company_item_total_amount' => $leadBranchItem->total_amount,
                    ])
                    ->toArray(),
            ];
        }

        $this->nr04RiskDegree = $this->lead->leadCompany->cnae?->nr04_risk_degree
            ?? $this->lead->leadCompany->company->cnaeModel?->nr04_risk_degree
            ?? '';

        $this->totalEmployeeCount = collect($this->companies)->sum(fn(array $company): float => $company['employee_count']);

        $this->tab = 'company-' . array_key_first($this->companies) . '-tab';

        if ($this->lead->deal) {
            $this->leadDealRegisteredAt = format_datetime($this->lead->leadDeals()->latest()->first()->deal_registered_at);
        }

        if ($this->lead->lost) {
            $this->leadLossRegisteredAt = format_datetime($this->lead->leadLosses()->latest()->first()->loss_registered_at);
        }
    }

    public function getCompanyInfo(int $index)
    {
        if ($this->companies[$index]['company_tax_id_number'] === '') {
            $this->companies[$index]['company_name'] = '';
            $this->companies[$index]['company_trading_name'] = '';
            $this->companies[$index]['address_zipcode'] = '';
            $this->companies[$index]['address_address'] = '';
            $this->companies[$index]['address_number'] = '';
            $this->companies[$index]['address_additional_info'] = '';
            $this->companies[$index]['address_district'] = '';
            $this->companies[$index]['address_city_id'] = '';
            $this->companies[$index]['address_city_name'] = '';
            $this->companies[$index]['address_state_id'] = '';
            $this->companies[$index]['address_state_abbreviation'] = '';
            $this->companies[$index]['cnae_id'] = '';

            return;
        }

        $companyDetails = (new ReceitaService())
            ->getCompanyDetails(unmask_cnpj($this->companies[$index]['company_tax_id_number']));

        /** @var \App\Models\City $city */
        $city = City::query()
            ->where('name', $companyDetails->municipio)
            ->first();

        /** @var \App\Models\Cnae $cnae */
        $cnae = Cnae::query()
            ->where('code', get_numbers($companyDetails->atividade_principal[0]->code))
            ->first();

        $this->companies[$index]['company_name'] = $companyDetails->nome;
        $this->companies[$index]['company_trading_name'] = $companyDetails->fantasia;
        $this->companies[$index]['address_zipcode'] = unmask_zipcode($companyDetails->cep);
        $this->companies[$index]['address_address'] = $companyDetails->logradouro;
        $this->companies[$index]['address_number'] = $companyDetails->numero;
        $this->companies[$index]['address_additional_info'] = $companyDetails->complemento;
        $this->companies[$index]['address_district'] = $companyDetails->bairro;
        $this->companies[$index]['address_city_id'] = $city->id;
        $this->companies[$index]['address_city_name'] = $city->name;
        $this->companies[$index]['address_state_id'] = $city->state_id;
        $this->companies[$index]['address_state_abbreviation'] = $city->state->abbreviation;
        $this->companies[$index]['cnae_id'] = $cnae->id;
    }

    public function openAddCompanyModal(): void
    {
        $this->addCompanyModalOpened = true;
        $this->emit('addCompanyModalOpened');
    }

    public function addCompany(?string $branchName = null): void
    {
        if (is_null($branchName)) {
            $this->companies[] = [
                'id' => 0,
                'company_name' => '',
                'company_trading_name' => '',
                'company_tax_id_number' => '',
                'branch_name' => '',
                'employee_count' => 0,
                'address_zipcode' => '',
                'address_address' => '',
                'address_number' => '',
                'address_additional_info' => '',
                'address_district' => '',
                'address_city_id' => '',
                'address_city_name' => '',
                'address_state_id' => '',
                'address_state_abbreviation' => '',
                'cnae_id' => '',
                'head_office' => false,
                'procedures' => [],
            ];
        } else {
            // Check if lead company has a Company relationship
            if ($this->lead->leadCompany->company) {
                /** @var \App\Models\Company $branch */
                $branch = $this->lead->leadCompany->company->companyBranches()
                    ->whereHas('branch', fn(Builder $query): Builder => $query->where('branch_name', $branchName))
                    ->first()
                    ->branch;

                $this->companies[] = [
                    'id' => 0,
                    'company_name' => $branch->name,
                    'company_trading_name' => $branch->trading_name,
                    'company_tax_id_number' => $branch->tax_id_number,
                    'branch_name' => $branch->branch_name,
                    'employee_count' => $branch->employee_count,
                    'address_zipcode' => $branch->zipcode,
                    'address_address' => $branch->address,
                    'address_number' => $branch->number,
                    'address_additional_info' => $branch->additional_info,
                    'address_district' => $branch->district,
                    'address_city_id' => City::query()
                        ->where('name', $branch->city)
                        ->first()
                        ?->id,
                    'address_city_name' => $branch->city,
                    'address_state_id' => State::query()
                        ->where('abbreviation', $branch->state)
                        ->first()
                        ?->id,
                    'address_state_abbreviation' => $branch->state,
                    'cnae_id' => $branch->cnae_id,
                    'head_office' => false,
                    'procedures' => [],
                ];
            } else {
                // Load from lead company branches
                $leadCompanyBranch = $this->lead->leadCompany->leadCompanyBranches()
                    ->where('branch_name', $branchName)
                    ->first();

                if ($leadCompanyBranch) {
                    $this->companies[] = [
                        'id' => 0,
                        'company_name' => $leadCompanyBranch->name,
                        'company_trading_name' => $leadCompanyBranch->trading_name,
                        'company_tax_id_number' => $leadCompanyBranch->friendly_tax_id_number,
                        'branch_name' => $leadCompanyBranch->branch_name,
                        'employee_count' => $leadCompanyBranch->employee_count,
                        'address_zipcode' => $leadCompanyBranch->zipcode,
                        'address_address' => $leadCompanyBranch->address,
                        'address_number' => $leadCompanyBranch->number,
                        'address_additional_info' => $leadCompanyBranch->additional_info,
                        'address_district' => $leadCompanyBranch->district,
                        'address_city_id' => $leadCompanyBranch->city_id,
                        'address_city_name' => $leadCompanyBranch->city,
                        'address_state_id' => $leadCompanyBranch->state_id,
                        'address_state_abbreviation' => $leadCompanyBranch->state,
                        'cnae_id' => $leadCompanyBranch->cnae_id,
                        'head_office' => false,
                        'procedures' => [],
                    ];
                }
            }
        }

        if (count($this->companies) === 1) {
            $this->tab = 'company-' . array_key_first($this->companies) . '-tab';
        }

        $this->closeAddCompanyModal();
    }

    public function closeAddCompanyModal(): void
    {
        $this->addCompanyModalOpened = false;
        $this->emit('addCompanyModalClosed');
    }

    public function addProposalItem(): void
    {
        $this->procedures[] = [
            'lead_proposal_item_id' => 0,
            'lead_proposal_item_procedure_id' => '',
            'lead_proposal_item_procedure_name' => '',
            'lead_proposal_item_type' => '',
            'lead_proposal_item_quantity' => collect($this->companies)->sum(fn(array $company): float => $company['employee_count']),
            'lead_proposal_item_minimum_amount' => 0,
            'lead_proposal_item_unit_amount' => 0,
            'lead_proposal_item_discount_percentage' => 0,
            'lead_proposal_item_discount_amount' => 0,
            'lead_proposal_item_addition_percentage' => 0,
            'lead_proposal_item_addition_amount' => 0,
            'lead_proposal_item_total_amount' => 0,
        ];

        $this->emit('proposalItemAdded', [
            'count' => count($this->procedures) - 1,
        ]);
    }

    public function addCompanyProcedure(int $index): void
    {
        $this->companies[$index]['procedures'][] = [
            'lead_proposal_company_item_id' => 0,
            'lead_proposal_company_item_procedure_id' => '',
            'lead_proposal_company_item_procedure_name' => '',
            'lead_proposal_company_item_type' => '',
            'lead_proposal_company_item_quantity' => 1,
            'lead_proposal_company_item_unit_amount' => 0,
            'lead_proposal_company_item_discount_percentage' => 0,
            'lead_proposal_company_item_discount_amount' => 0,
            'lead_proposal_company_item_addition_percentage' => 0,
            'lead_proposal_company_item_addition_amount' => 0,
            'lead_proposal_company_item_total_amount' => 0,
        ];

        $this->emit('proposalCompanyItemAdded', [
            'index' => $index,
            'count' => count($this->companies[$index]['procedures']) - 1,
        ]);
    }

    public function deleteCompanyProcedure(int $index, int $procedureIndex): void
    {
        unset($this->companies[$index]['procedures'][$procedureIndex]);

        $this->emit('proposalCompanyItemDeleted', [
            'companyIndex' => $index,
            'procedures' => $this->companies[$index]['procedures']
        ]);
    }

    public function deleteProposalItem(int $procedureIndex): void
    {
        unset($this->procedures[$procedureIndex]);
        $this->emit('proposalItemDeleted', $this->procedures);
    }

    public function updatedCompanies(mixed $value, mixed $key)
    {
        $explodedKey = explode('.', $key);

        if ($explodedKey[1] === 'employee_count') {
            $this->updateTotalEmployeeCount();
        }

        if ($explodedKey[1] === 'address_zipcode') {
            if (strlen($value) !== 8) {
                return;
            }

            $addressDetails = (new ViaCepZipcodeService())->getZipcodeDetails($value);

            $this->companies[$explodedKey[0]]['address_address'] = $addressDetails->logradouro;
            $this->companies[$explodedKey[0]]['address_district'] = $addressDetails->bairro;

            $this->emit('updateCompanyAddress', [
                'index' => $explodedKey[0],
                'address_city_id' => City::query()
                    ->where('name', $addressDetails->localidade)
                    ->first()
                    ?->id,
                'address_city_name' => $addressDetails->localidade,
                'address_state_id' => State::query()
                    ->where('abbreviation', $addressDetails->uf)
                    ->first()
                    ?->id,
                'address_state_abbreviation' => $addressDetails->uf,
            ]);
        }
    }

    public function updateTotalEmployeeCount(): void
    {
        $this->totalEmployeeCount = collect($this->companies)->sum(fn(array $company): float => $company['employee_count']);

        $this->lead['total_employee_count'] = $this->totalEmployeeCount;

        foreach ($this->procedures as $key => $procedure) {
            $this->procedures[$key]['quantity'] = $this->totalEmployeeCount;
        }

        $this->emit('updateProceduresEmployeeCount', collect($this->companies)->sum(fn(array $company): float => $company['employee_count']));
    }

    public function updateLead()
    {
        $requestData = [
            'lead_company_id' => $this->leadCompanyId,
            'lead_company_contact_id' => $this->leadCompanyContactId,
            'bid' => $this->bid,
            'lead_items' => $this->procedures,
            'lead_branches' => $this->companies,
        ];

        $this->lead = \App\Actions\Lead\EditLead::run($this->lead, $requestData);

        return redirect_success('leads.edit', __('leads.responses.update.success'), $this->lead->id);
    }

    public function openViewProposalModal(): void
    {
        $this->emit('openViewProposalModal');
    }

    public function openReplicateCompanyProcedureModal(int $companyIndex, int $procedureIndex): void
    {
        $this->currentReplicateCompanyProcedureCompanyKey = $companyIndex;
        $this->currentReplicateCompanyProcedureProcedureKey = $procedureIndex;
        $this->emit('openReplicateCompanyProcedureModal');
    }

    public function replicateProcedureToUnits(): void
    {
        $errors = [];

        foreach ($this->companies as $key => $company) {
            if ((int) $key === (int) $this->currentReplicateCompanyProcedureCompanyKey) {
                continue;
            }

            if (
                is_null($this->companies[$key]['address_city_id'])
                || trim($this->companies[$key]['address_city_id']) === ''
                || (int) $this->companies[$key]['address_city_id'] === 0
            ) {
                if ((is_null($company['branch_name']) || $company['branch_name'] === '')) {
                    $errors[$key] = "A cidade da unidade Filial $key não foi informada.";
                } else {
                    $errors[$key] = "A cidade da unidade {$company['branch_name']} não foi informada.";
                }

                continue;
            }

            /** @var array $crmProcedureData */
            $crmProcedureData = GetProcedureDataForCrm::run(
                Procedure::find($this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_id']),
                $this->companies[$key]['address_city_id'],
                $this->companies[$key]['employee_count'],
            );

            $quantity = $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_quantity'];
            $unitAmount = $crmProcedureData['amount'];
            $discountAmount = ($quantity * $unitAmount) * ($this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_discount_percentage'] / 100);
            $additionAmount = ($quantity * $unitAmount) * ($this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_addition_percentage'] / 100);

            $this->companies[$key]['procedures'][] = [
                'lead_proposal_company_item_id' => 0,
                'lead_proposal_company_item_procedure_id' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_id'],
                'lead_proposal_company_item_procedure_name' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_procedure_name'],
                'lead_proposal_company_item_type' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_type'],
                'lead_proposal_company_item_quantity' => $quantity,
                'lead_proposal_company_item_unit_amount' => $unitAmount,
                'lead_proposal_company_item_discount_percentage' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_discount_percentage'],
                'lead_proposal_company_item_discount_amount' => $discountAmount,
                'lead_proposal_company_item_addition_percentage' => $this->companies[$this->currentReplicateCompanyProcedureCompanyKey]['procedures'][$this->currentReplicateCompanyProcedureProcedureKey]['lead_proposal_company_item_addition_percentage'],
                'lead_proposal_company_item_addition_amount' => $additionAmount,
                'lead_proposal_company_item_total_amount' => ($quantity * $unitAmount) - $discountAmount + $additionAmount,
            ];
        }

        $this->closeReplicateCompanyProcedureModal();

        if (!empty($errors)) {
            $message = 'O processo de replicação foi finalizado. Contudo, os seguintes erros foram encontrados: <br><ul>' . implode('', array_map(fn(string $error): string => "<li>$error</li>", $errors)) . '</ul>';
            $message .= 'Será necessário corrigir os dados restantes manualmente.';

            $this->emit('showReplicationErrorModal', $message);
        }
    }

    public function closeReplicateCompanyProcedureModal(): void
    {
        $this->currentReplicateCompanyProcedureCompanyKey = null;
        $this->currentReplicateCompanyProcedureProcedureKey = null;
    }

    public function removeCompany(int $index): void
    {
        unset($this->companies[$index]);

        $this->updateTotalEmployeeCount();

        $this->tab = 'company-' . array_key_first($this->companies) . '-tab';
    }

    public function addSignatory(): void
    {
        $this->leadProposalContractSignatories[] = [
            'name' => null,
            'tax_id_number' => null,
        ];

        $this->emit('signatoryAdded');
    }

    public function removeSignatory(int $index): void
    {
        unset($this->leadProposalContractSignatories[$index]);
        $this->emit('signatoryRemoved');
    }
}
