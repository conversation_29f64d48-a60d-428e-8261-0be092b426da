<?php

namespace App\Models;

use App\Models\Concerns\LeadCompanyBranch\HandlesLeadCompanyBranchAttributes;
use App\Models\Concerns\LeadCompanyBranch\HandlesLeadCompanyBranchRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Lead company branch model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $lead_company_id
 * @property  int $cnae_id
 * @property  string $name
 * @property  string $trading_name
 * @property  string $tax_id_number
 * @property  string $branch_name
 * @property  string $state_registration_no
 * @property  string $city_registration_no
 * @property  string $zipcode
 * @property  string $address
 * @property  string $number
 * @property  string $additional_info
 * @property  string $district
 * @property  int $city_id
 * @property  string $city
 * @property  int $state_id
 * @property  string $state
 * @property  string $country
 * @property  int $employee_count
 * @property  bool $national_simple
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_tax_id_number
 *
 * @property  \App\Models\LeadCompany $leadCompany
 * @property  \App\Models\Cnae $cnae
 * @property  \App\Models\City $city
 * @property  \App\Models\State $state
 */
class LeadCompanyBranch extends Model
{
    use HandlesLeadCompanyBranchAttributes;
    use HandlesLeadCompanyBranchRelationships;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lead_company_id',
        'cnae_id',
        'name',
        'trading_name',
        'tax_id_number',
        'branch_name',
        'state_registration_no',
        'city_registration_no',
        'zipcode',
        'address',
        'number',
        'additional_info',
        'district',
        'city_id',
        'city',
        'state_id',
        'state',
        'country',
        'employee_count',
        'national_simple',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'employee_count' => 'int',
        'national_simple' => 'bool',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        parent::booted();

        static::saving(function (self $leadCompanyBranch): void {
            $leadCompanyBranch->state_registration_no = $leadCompanyBranch->exempt_from_state_registration
                ? 'ISENTO'
                : $leadCompanyBranch->state_registration_no;

            $leadCompanyBranch->city_id ??= City::query()
                ->where('name', $leadCompanyBranch->city)
                ->first()
                ?->id;

            $leadCompanyBranch->state_id ??= State::query()
                ->where('abbreviation', $leadCompanyBranch->state)
                ->first()
                ?->id;
        });
    }
}
