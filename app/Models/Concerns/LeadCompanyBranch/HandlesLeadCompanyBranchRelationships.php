<?php

namespace App\Models\Concerns\LeadCompanyBranch;

use App\Models\City;
use App\Models\Cnae;
use App\Models\LeadCompany;
use App\Models\State;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesLeadCompanyBranchRelationships
{
    /**
     * Load the lead company relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function leadCompany(): BelongsTo
    {
        return $this->belongsTo(LeadCompany::class);
    }

    /**
     * Load the CNAE relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cnae(): BelongsTo
    {
        return $this->belongsTo(Cnae::class);
    }

    /**
     * Load the city relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Load the state relationship.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }


}
