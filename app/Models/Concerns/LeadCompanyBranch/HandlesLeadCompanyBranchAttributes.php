<?php

namespace App\Models\Concerns\LeadCompanyBranch;

use Illuminate\Support\Str;

trait HandlesLeadCompanyBranchAttributes
{
    /**
     * Mutator for the "trading name" attribute.
     *
     * @return void
     */
    public function setTradingNameAttribute(mixed $value): void
    {
        $this->attributes['trading_name'] = trim(Str::ascii(mb_strtoupper($value)));
    }

    /**
     * Mutator for the "tax ID number" attribute.
     *
     * @return void
     */
    public function setTaxIdNumberAttribute(mixed $value): void
    {
        $this->attributes['tax_id_number'] = preg_replace('/[^0-9]+/', '', trim($value));
    }

    /**
     * Mutator for the "state registration no" attribute.
     *
     * @return void
     */
    public function setStateRegistrationNoAttribute(mixed $value): void
    {
        $this->attributes['state_registration_no'] = $value ?? 'ISENTA';
    }

    /**
     * Mutator for the "zipcode" attribute.
     *
     * @return void
     */
    public function setZipcodeAttribute(mixed $value): void
    {
        $this->attributes['zipcode'] = str_pad(preg_replace('/[^0-9]+/', '', trim($value)), 8, '0', STR_PAD_LEFT);
    }

    /**
     * Mutator for the "address" attribute.
     *
     * @return void
     */
    public function setAddressAttribute(mixed $value): void
    {
        $this->attributes['address'] = trim(mb_strtoupper($value));
    }

    /**
     * Mutator for the "additional info" attribute.
     *
     * @return void
     */
    public function setAdditionalInfoAttribute(mixed $value): void
    {
        $this->attributes['additional_info'] = trim(mb_strtoupper($value));
    }

    /**
     * Mutator for the "district" attribute.
     *
     * @return void
     */
    public function setDistrictAttribute(mixed $value): void
    {
        $this->attributes['district'] = trim(mb_strtoupper($value));
    }

    /**
     * Mutator for the "city" attribute.
     *
     * @return void
     */
    public function setCityAttribute(mixed $value): void
    {
        $this->attributes['city'] = trim(mb_strtoupper($value));
    }

    /**
     * Mutator for the "cnae" attribute.
     *
     * @return void
     */
    public function setCnaeAttribute(mixed $value): void
    {
        $this->attributes['cnae'] = unmask_cnae($value);
    }

    /**
     * Accessor for the "friendly CNAE" attribute.
     *
     * @return string
     */
    public function getFriendlyCnaeAttribute(): string
    {
        return mask_cnae($this->cnae);
    }

    /**
     * Mutator for the "phone" attribute.
     *
     * @param  mixed $value
     * @return void
     */
    public function setPhoneAttribute(mixed $value): void
    {
        $this->attributes['phone'] = Str::remove(' ', unmask_phone($value));
    }

    /**
     * Accessor for the "friendly tax ID number" attribute.
     *
     * @return string
     */
    public function getFriendlyTaxIdNumberAttribute(): string
    {
        return mask_cnpj($this->tax_id_number);
    }
}
