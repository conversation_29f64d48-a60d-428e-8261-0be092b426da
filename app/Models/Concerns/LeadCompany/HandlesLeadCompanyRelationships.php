<?php

namespace App\Models\Concerns\LeadCompany;

use App\Models\Cnae;
use App\Models\Company;
use App\Models\CrmConversion;
use App\Models\LeadCompanyBranch;
use App\Models\LeadCompanyContact;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HandlesLeadCompanyRelationships
{
    public function crmConversion(): BelongsTo
    {
        return $this->belongsTo(CrmConversion::class);
    }

    public function cnae(): BelongsTo
    {
        return $this->belongsTo(Cnae::class);
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class);
    }

    public function leadCompanyContacts(): HasMany
    {
        return $this->hasMany(LeadCompanyContact::class);
    }

    public function leadCompanyBranches(): HasMany
    {
        return $this->hasMany(LeadCompanyBranch::class);
    }
}
