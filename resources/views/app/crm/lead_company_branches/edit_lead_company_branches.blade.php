@extends('layouts.app')

@section('content')
    <x-card :title="__('lead_company_branches.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true">
        <x-form.edit-form :$resourceRoute :routeParameters="[$leadCompany->id, $leadCompanyBranch->id]">
            <x-form.row>
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :required="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="trading_name" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="tax_id_number" customClass="cnpj-input" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="branch_name" :required="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="state_registration_no" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="city_registration_no" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="zipcode" customClass="zipcode-input" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="address" />
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="number" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="additional_info" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="district" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.select md="6" lg="6" :$resourceRoute :$action field="city_id" :wireIgnore="true" />
                <x-input.select md="6" lg="6" :$resourceRoute :$action field="state_id" :wireIgnore="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="country" />
                <x-input.number md="3" lg="3" :$resourceRoute :$action field="employee_count" :required="true" />
                <x-input.checkbox md="3" lg="3" :$resourceRoute :$action field="national_simple" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.select md="12" lg="12" :$resourceRoute :$action field="cnae_id" :wireIgnore="true" />
            </x-form.row>
        </x-form.edit-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.13.4/jquery.mask.min.js"></script>

    <script>
        $(document).ready(function() {
            $('.cnpj-input').mask('00.000.000/0000-00');
            $('.zipcode-input').mask('00000-000');

            initializeSelect2('city_id', "{{ route('cities.get_by_name') }}", 'Digite o nome da cidade', {{ $leadCompanyBranch->city_id ?? 'null' }});
            initializeSelect2('state_id', "{{ route('states.get_by_name_or_abbreviation') }}", 'Digite o nome ou sigla do estado', {{ $leadCompanyBranch->state_id ?? 'null' }});
            initializeSelect2('cnae_id', "{{ route('cnaes.get_by_code_or_description') }}", 'Digite o código ou descrição do CNAE', {{ $leadCompanyBranch->cnae_id ?? 'null' }});
        });
    </script>
@endsection
