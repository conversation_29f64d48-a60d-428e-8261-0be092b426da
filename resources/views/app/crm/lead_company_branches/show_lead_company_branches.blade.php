@extends('layouts.app')

@section('content')
    <x-card :title="__('lead_company_branches.cards.show.header.title')" :cardHeaderTitle="true" :backButton="true">
        <x-form.show-form>
            <x-form.row>
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="trading_name" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="tax_id_number" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="branch_name" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="state_registration_no" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="city_registration_no" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="zipcode" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="address" :readonly="true" />
                <x-input.text md="3" lg="3" :$resourceRoute :$action field="number" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="additional_info" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="district" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="city" :readonly="true" />
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="state" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="6" lg="6" :$resourceRoute :$action field="country" :readonly="true" />
                <x-input.number md="3" lg="3" :$resourceRoute :$action field="employee_count" :readonly="true" />
                <x-input.checkbox md="3" lg="3" :$resourceRoute :$action field="national_simple" :readonly="true" />
            </x-form.row>
            <x-form.row :marginTop="true">
                <x-input.text md="12" lg="12" :$resourceRoute :$action field="cnae.description" :readonly="true" />
            </x-form.row>
        </x-form.show-form>
    </x-card>
@endsection
