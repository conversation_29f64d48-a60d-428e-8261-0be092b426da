<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('lead_company_branches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_company_id')->constrained();
            $table->foreignId('cnae_id')->nullable()->constrained();
            $table->string('name');
            $table->string('trading_name')->nullable();
            $table->string('tax_id_number', 14)->nullable();
            $table->string('branch_name');
            $table->string('state_registration_no')->nullable();
            $table->string('city_registration_no')->nullable();
            $table->string('zipcode', 8)->nullable();
            $table->string('address')->nullable();
            $table->string('number')->nullable();
            $table->string('additional_info')->nullable();
            $table->string('district')->nullable();
            $table->foreignId('city_id')->nullable()->constrained();
            $table->string('city')->nullable();
            $table->foreignId('state_id')->nullable()->constrained();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->integer('employee_count');
            $table->boolean('national_simple');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('lead_company_branches');
    }
};
